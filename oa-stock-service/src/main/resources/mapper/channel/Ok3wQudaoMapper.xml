<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.nc.channel.mapper.Ok3wQudaoMapper">

  <resultMap id="ok3wQudaoMap" type="com.jiuji.oa.nc.channel.entity.Ok3wQudao">
                  <id property="id" column="id"/>
                        <result property="kinds" column="kinds"/>
                        <result property="company" column="company"/>
                        <result property="username" column="username"/>
                        <result property="tel" column="tel"/>
                        <result property="fax" column="fax"/>
                        <result property="mobile" column="mobile"/>
                        <result property="qq" column="QQ"/>
                        <result property="wangwang" column="wangwang"/>
                        <result property="address" column="address"/>
                        <result property="area" column="Area"/>
                        <result property="comment" column="comment"/>
                        <result property="adddate" column="adddate"/>
                        <result property="inuser" column="inuser"/>
                        <result property="ispass" column="ispass"/>
                        <result property="email" column="email"/>
                        <result property="insourceid" column="insourceid"/>
                        <result property="companyJc" column="company_jc"/>
                        <result property="gZh1" column="g_zh1"/>
                        <result property="gHm1" column="g_hm1"/>
                        <result property="gKhh1" column="g_khh1"/>
                        <result property="gZh2" column="g_zh2"/>
                        <result property="gHm2" column="g_hm2"/>
                        <result property="gKhh2" column="g_khh2"/>
                        <result property="sZh1" column="s_zh1"/>
                        <result property="sHm1" column="s_hm1"/>
                        <result property="sKhh1" column="s_khh1"/>
                        <result property="sZh2" column="s_zh2"/>
                        <result property="sHm2" column="s_hm2"/>
                        <result property="sKhh2" column="s_khh2"/>
                        <result property="cwFzr" column="cw_fzr"/>
                        <result property="cwLxfs" column="cw_lxfs"/>
                        <result property="sZh3" column="s_zh3"/>
                        <result property="sHm3" column="s_hm3"/>
                        <result property="sKhh3" column="s_khh3"/>
                        <result property="sZh4" column="s_zh4"/>
                        <result property="sHm4" column="s_hm4"/>
                        <result property="sKhh4" column="s_khh4"/>
                        <result property="sZh5" column="s_zh5"/>
                        <result property="sHm5" column="s_hm5"/>
                        <result property="sKhh5" column="s_khh5"/>
                        <result property="sZh6" column="s_zh6"/>
                        <result property="sHm6" column="s_hm6"/>
                        <result property="sKhh6" column="s_khh6"/>
                        <result property="sZh7" column="s_zh7"/>
                        <result property="sHm7" column="s_hm7"/>
                        <result property="sKhh7" column="s_khh7"/>
                        <result property="sZh8" column="s_zh8"/>
                        <result property="sHm8" column="s_hm8"/>
                        <result property="sKhh8" column="s_khh8"/>
                        <result property="kfp" column="kfp"/>
                        <result property="authorizeid" column="authorizeid"/>
                        <result property="signstarttime" column="signStartTime"/>
                        <result property="signendtime" column="signEndTime"/>
                        <result property="cityid" column="cityid"/>
                        <result property="pid" column="pid"/>
                        <result property="zid" column="zid"/>
                        <result property="did" column="did"/>
                        <result property="companynature" column="CompanyNature"/>
                        <result property="qudaonature" column="QuDaoNature"/>
                        <result property="qudaolevel" column="QuDaoLevel"/>
                        <result property="paytype" column="PayType"/>
                        <result property="registeredcapital" column="RegisteredCapital"/>
                        <result property="legalrepresent" column="LegalRepresent"/>
                        <result property="legalmobile" column="LegalMobile"/>
                        <result property="weixin" column="WeiXin"/>
                        <result property="shouhoucontacts" column="shouhouContacts"/>
                        <result property="shouhoumobile" column="shouhouMobile"/>
                        <result property="yajin" column="yajin"/>
                        <result property="samekindqudao" column="SameKindQuDao"/>
                        <result property="loanamountM" column="LoanAmount_M"/>
                        <result property="loanamount" column="LoanAmount"/>
                        <result property="caiwucheckuser" column="CaiWuCheckUser"/>
                        <result property="caiwuchecktime" column="CaiWuCheckTime"/>
                        <result property="shenjicheckuser" column="ShenJiCheckUser"/>
                        <result property="shenjichecktime" column="ShenJiCheckTime"/>
                        <result property="pandianuser" column="PanDianUser"/>
                        <result property="pandiantime" column="PanDianTime"/>
                        <result property="kemu" column="kemu"/>
                        <result property="charger" column="charger"/>
                        <result property="comment1" column="comment1"/>
                        <result property="seltpurchase" column="seltPurchase"/>
                        <result property="cooperationid" column="CooperationId"/>
                        <result property="customcode" column="CustomCode"/>
                        <result property="userid" column="userid"/>
                        <result property="channeltype" column="ChannelType"/>
                        <result property="channelscale" column="ChannelScale"/>
                        <result property="deposithasreceipt" column="DepositHasReceipt"/>
                        <result property="cids" column="cids"/>
                        <result property="brandid" column="brandid"/>
                        <result property="receiver" column="Receiver"/>
                        <result property="shippingaddress" column="ShippingAddress"/>
                        <result property="bindareaid" column="bindAreaId"/>
                        <result property="isauction" column="isAuction"/>
                        <result property="gCity1" column="g_city1"/>
                        <result property="gIsSameCity1" column="g_is_same_city1"/>
                        <result property="gBankNumber1" column="g_bank_number1"/>
                        <result property="gCity2" column="g_city2"/>
                        <result property="gIsSameCity2" column="g_is_same_city2"/>
                        <result property="gBankNumber2" column="g_bank_number2"/>
                        <result property="enterType" column="enter_type"/>
                        <result property="afterAddress" column="after_address"/>
                        <result property="afterCityid" column="after_cityId"/>
                        <result property="businessCityid" column="business_cityId"/>
                        <result property="smoney" column="Smoney"/>
                        <result property="classification" column="classification"/>
            </resultMap>
    <select id="queryChannelPage" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelPageVo">
          WITH selectTemp AS (
          SELECT
          TOP 100 PERCENT ROW_NUMBER() OVER (
          ORDER BY t3.id desc) as __row_number__,
          t3.id as id,
          t3.company as company,
          t3.cids as cids,
          t3.brandid as brandId,
          t3.ispass as isPass,
          t3.address as address,
          t3.comment as comment
          from Ok3w_qudao t3 with(nolock)
          where  t3.id in (select distinct(t1.id) from Ok3w_qudao t1 with(nolock)
          left join channel_kind_link t2 with(nolock) on t1.id = t2.channel_id
          left join channel_province_city t3 with(nolock) on t1.id = t3.channel_id
          <include refid="conmonWhere"></include>
          ))
          SELECT
          *
          FROM
          selectTemp
          WHERE
          __row_number__ BETWEEN #{begin} AND #{end}
          ORDER BY
          __row_number__
    </select>
    <select id="queryChannelPageV1" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelPageVo">
        WITH selectTemp AS (
        SELECT
        TOP 100 PERCENT ROW_NUMBER() OVER (
        ORDER BY t3.id desc) as __row_number__,
        t3.id as id,
        t3.company as company,
        t3.company_jc as companyJc,
        t3.cids as cids,
        t3.brandid as brandId,
        t3.ispass as isPass,
        t3.address as address,
        t3.comment as comment
        from Ok3w_qudao t3 with(nolock)
        where  t3.id in (select distinct(t1.id) from Ok3w_qudao t1 with(nolock)
        left join channel_kind_link t2 with(nolock) on t1.id = t2.channel_id
        left join channel_province_city t3 with(nolock) on t1.id = t3.channel_id
        left join qudaoAccounts t4 with(nolock) on t2.id = t4.link_id and t4.isDel=0
        <include refid="commonWhereV1"></include>
        ))
        SELECT
        *
        FROM
        selectTemp
        WHERE
        __row_number__ BETWEEN #{begin} AND #{end}
        ORDER BY
        __row_number__
    </select>
      <select id="getChannelSimpleInfo" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelSimpleVo">
            SELECT TOP 20 owq.id, owq.company, owq.company_jc FROM Ok3w_qudao owq with(nolock)
            <where>
                  <if test="id != null">
                        owq.id = #{id}
                  </if>
                  <if test="searchKey != null and searchKey != ''">
                        OR owq.company LIKE CONCAT('%',#{searchKey},'%')
                        OR owq.company_jc LIKE CONCAT('%',#{searchKey},'%')
                  </if>
            </where>
      </select>
      <select id="getTotalCount" resultType="java.lang.Integer">
            select Count(*)
            from Ok3w_qudao t3 with(nolock)
            where  t3.id in (select distinct(t1.id) from Ok3w_qudao t1 with(nolock)
            left join channel_kind_link t2 with(nolock) on t1.id = t2.channel_id
            left join channel_province_city t3 with(nolock) on t1.id = t3.channel_id
            <include refid="conmonWhere"></include>)
      </select>
    <select id="getTotalCountV1" resultType="java.lang.Integer">
        select Count(*)
        from Ok3w_qudao t3 with(nolock)
        where  t3.id in (select distinct(t1.id) from Ok3w_qudao t1 with(nolock)
        left join channel_kind_link t2 with(nolock) on t1.id = t2.channel_id
        left join channel_province_city t3 with(nolock) on t1.id = t3.channel_id
        left join qudaoAccounts t4 with(nolock) on t2.id = t4.link_id and t4.isDel=0
        <include refid="commonWhereV1"></include>)
    </select>
    <select id="getChannelStockQuery" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelStockQueryVo">
        SELECT
        owq.id as id,
        owq.company_jc as companyJc
        FROM
        Ok3w_qudao owq with(nolock) left join channel_kind_link ckl with(nolock) on owq.id = ckl.channel_id
        WHERE
        ckl.kind =3 AND ckl.channel_state = 1
        <if test="req.channelName != null and req.channelName !=''">
            and (owq.company_jc LIKE CONCAT('%',#{req.channelName},'%'))
        </if>
        <if test="req.insourceId != null">
            and owq.insourceid = #{req.insourceId}
        </if>
        <if test="req.id != null">
            and owq.id = #{req.id}
        </if>
    </select>

      <select id="getChannelStockQueryOld" resultType="com.jiuji.oa.nc.channel.vo.res.ChannelStockQueryVo">
            SELECT
            owq.id as id,
            owq.company_jc as companyJc
            FROM
            Ok3w_qudao owq with(nolock)
            WHERE
            owq.kinds =3 AND owq.ispass = 1
            <if test="req.channelName != null and req.channelName !=''">
                  and owq.company_jc LIKE CONCAT('%',#{req.channelName},'%')
            </if>
            <if test="req.insourceId != null">
                  and owq.insourceid = #{req.insourceId}
            </if>
      </select>
    <select id="getChannelListExport" resultType="com.jiuji.oa.nc.channel.vo.dto.ChannelListExportDto">
        select qudao.id,
               qudao.company,
               qudao.company_jc as companyJc,
               (CASE qudao.CompanyNature
                    when 1 then '个体经营'
                    when 2 then '公司'
                    when 3 then '上市公司' end)                             as CompanyNature,
               qudao.CustomCode,
               (CASE WHEN qudao.seltPurchase = 0 THEN '否' ELSE '是' END) as seltPurchase,
               t.kinds,
               t.channelStates,
               t.settlementType,
               t.invoicingFlag,
               t.invoiceType,
               (CASE when t.subject=',' then null else t.subject end )as subject,
               t.margin,
               t.depositHasReceipt,
               t.productCategory,
               (CASE when t.partnerCompany=',' then null else t.partnerCompany end )as partnerCompany

        from Ok3w_qudao qudao with (nolock)
         left join (select channel_id,
                           STUFF((select ',' + convert(nvarchar(50), kind)
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as kinds,
                           STUFF((select ',' + convert(nvarchar(50), (CASE channel_state
                                                                          when 0 then '申请中'
                                                                          when 1 then '合作中'
                                                                          when 2 then '停止合作'
                                                                          when 3 then '作废' end))
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as channelStates,
                           STUFF((select ',' + convert(nvarchar(50), (CASE settlement_type
                                                                          when 1 then '先款后货'
                                                                          when 2 then '七天账期'
                                                                          when 3 then '十五天账期'
                                                                          when 4 then '三十天账期'
                                                                          when 5 then '货到付款' end))
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as settlementType,
                           STUFF((select ',' + convert(nvarchar(50), (CASE invoicing_flag
                                                                          when 1 then '能'
                                                                          when 0 then '否' end))
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as invoicingFlag,
                           STUFF((select ',' + convert(nvarchar(50), (CASE invoice_type
                                                                          when 0 then '增值税普通发票'
                                                                          when 1 then '增值税专用发票' end))
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as invoiceType,
                           STUFF((select ',' + convert(nvarchar(50), subject)
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as subject,



                           STUFF((select ',' + convert(nvarchar(50), margin)
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as margin,
                           STUFF((select ',' + convert(nvarchar(50), (CASE deposit_has_receipt
                                                                          when 1 then '有'
                                                                          when 0 then '没有' end))
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as depositHasReceipt,
                           STUFF((select ',' + convert(nvarchar(50), partner_company)
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as partnerCompany,
                           STUFF((select ',' + convert(nvarchar(50), product_category)
                                  FROM channel_kind_link with (nolock)
                                  WHERE channel_id = c.channel_id
                                  FOR XML Path('')), 1, 1, '') as productCategory
                    from channel_kind_link c with (nolock)
                    group by (channel_id)) t on t.channel_id = qudao.id

         <where>
             <if test="channlIds != null and channlIds.size()>0">
                 qudao.id in
                 <foreach collection="channlIds" item="item" open="(" close=")" separator=",">
                     #{item}
                 </foreach>
             </if>
         </where>
        order by id desc
    </select>
  <resultMap id="listChannelStateResultMap"
    type="com.jiuji.oa.nc.channel.vo.dto.ChannelListStateDTO">
    <id property="id" column="channel_id"/>
    <collection property="kindList" ofType="com.jiuji.oa.nc.channel.vo.dto.ChannelKindStateDTO">
      <result column="kind" property="kind"/>
      <result column="channel_state" property="state"/>
    </collection>
  </resultMap>
  <select id="listChannelState" resultMap="listChannelStateResultMap">
    SELECT
    t2.channel_id,
    t2.kind,
    t2.channel_state
    from
    channel_kind_link t2 WITH(NOLOCK)
    <where>
      t2.channel_id in
      <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </where>
    order by 1,2
  </select>
    <select id="getSmoney" resultType="com.jiuji.oa.nc.channel.vo.dto.SmoneyDTO">

        select id, Smoney as smoney from Ok3w_qudao with (nolock ) where id=#{channelId}
    </select>

    <select id="selectAllList" resultType="com.jiuji.oa.nc.channel.vo.dto.Ok3wQuDaoDto">
        select qd.id,qd.company_jc companyJc,li.kind kinds,qd.insourceid insourceId,qd.authorizeid authorizeId,qd.kemu,qd.charger,qd.CustomCode customCode
        from Ok3w_qudao qd with(nolock)
        left join channel_kind_link li with(nolock) on qd.id = li.channel_id
        where li.channel_state=1 and qd.company_jc is not null
    </select>
    <select id="selectAllListV2" resultType="com.jiuji.oa.nc.channel.vo.dto.Ok3wQuDaoDto">
        select qd.id,qd.company_jc companyJc,li.kind kinds,qd.insourceid insourceId,qd.authorizeid authorizeId,qd.kemu,qd.charger,qd.CustomCode customCode,li.channel_state
        from Ok3w_qudao qd with(nolock)
        left join channel_kind_link li with(nolock) on qd.id = li.channel_id
        where qd.company_jc is not null
    </select>
    <select id="selectAllQudaoInfoList" resultType="com.jiuji.oa.nc.channel.vo.dto.Ok3wQuDaoInfoDto">
        select qd.id,qd.company_jc companyJc,li.kind kinds,li.channel_state,
               qd.seltPurchase,li.partner_company partnerCompany,li.id channelLinkId
        from Ok3w_qudao qd with(nolock)
        left join channel_kind_link li with(nolock) on qd.id = li.channel_id
        where qd.company_jc is not null
    </select>


    <sql id="conmonWhere">
            <where>
                  t1.authorizeid = #{params.authorizeId}
                  <if test="params.kind != null">
                        and t2.kind = #{params.kind}
                  </if>
                  <if test="params.isTaxModel != null and params.isTaxModel">
                        and t2.kind = 3 and t2.invoicing_flag = 1
                  </if>
                  <if test="params.passFlag != null">
                        and t2.channel_state = #{params.passFlag}
                  </if>
                  <if test="params.attrBusinessSection != null">
                        and t1.attr_business_section = #{params.attrBusinessSection}
                  </if>
                  <if test="params.cooperationId != null">
                        and t2.partner_company = #{params.cooperationId}
                  </if>
                  <if test="params.attributionArea != null and params.attributionArea.size()>0">
                        and
                        <foreach collection="params.attributionArea" item="item" open="(" close=")" separator="or">
                              t1.attribution_area like CONCAT('%', #{item},'%')
                        </foreach>
                  </if>
                  <if test="(params.provinceIdList != null and params.provinceIdList.size()>0)
                              ||(params.cityIdList != null and params.cityIdList.size()>0)
                              ||(params.countyIdList != null and params.countyIdList.size()>0)">
                        and(
                              1 > 1
                              <if test="(params.provinceIdList != null and params.provinceIdList.size()>0) ">
                                    or t3.province_id in
                                    <foreach collection="params.provinceIdList" item="item" open="(" close=")" separator=",">
                                          #{item}
                                    </foreach>
                              </if>
                              <if test="(params.cityIdList != null and params.cityIdList.size()>0) ">
                                    or t3.city_id in
                                    <foreach collection="params.cityIdList" item="item" open="(" close=")" separator=",">
                                          #{item}
                                    </foreach>
                              </if>
                              <if test="(params.countyIdList != null and params.countyIdList.size()>0) ">
                                    or t3.county_id in
                                    <foreach collection="params.countyIdList" item="item" open="(" close=")" separator=",">
                                          #{item}
                                    </foreach>
                              </if>
                        )

                  </if>
                  <if test="params.idList != null and params.idList.size()>0">
                        and t1.id in
                        <foreach collection="params.idList" item="item" open="(" close=")" separator=",">
                              #{item}
                        </foreach>
                  </if>
                  <if test="params.classification!=null">
                        AND t1.classification like CONCAT('%',#{params.classification},'%')
                  </if>
                  <!--                <if test="params.cidList != null and params.cidList.size()>0">-->
                  <!--                      and isnull(cids,0) in-->
                  <!--                      <foreach collection="params.cidList" item="item" open="(" close=")" separator=",">-->
                  <!--                            #{item}-->
                  <!--                      </foreach>-->
                  <!--                </if>-->
                  <if test="params.brandIdList != null and params.brandIdList.size()>0">
                        and
                        <foreach collection="params.brandIdList" item="item" open="(" close=")" separator=" or ">
                              t1.brandid like CONCAT('%,', #{item},',%')
                        </foreach>
                  </if>

                  <!--                <if test="params.cooperationId != null">-->
                  <!--                      AND CooperationId = #{params.cooperationId}-->
                  <!--                </if>-->
                  <!--                <if test="params.passFlag != null">-->
                  <!--                      AND ispass = #{params.passFlag}-->
                  <!--                </if>-->
                  <if test="params.channelLevel != null">
                        AND isnull(t1.QuDaoLevel,0) = #{params.channelLevel}
                  </if>
                  <if test="params.searchKey == 0 and params.searchWord!=null and params.searchWord!=''">
                        AND (t1.company like CONCAT('%',#{params.searchWord},'%') or t1.username like
                        CONCAT('%',#{params.searchWord},'%'))
                  </if>
                  <if test="params.searchKey == 1 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.tel like CONCAT('%',#{params.searchWord},'%')
                  </if>
                  <if test="params.searchKey == 2 and params.searchWord!=null and params.searchWord!=''">
                        AND (t1.QQ like CONCAT('%',#{params.searchWord},'%') or t1.wangwang like CONCAT('%',#{params.searchWord},'%'))
                  </if>
                  <if test="params.searchKey == 3 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.id = #{params.searchWord}
                  </if>
                  <if test="params.searchKey == 4 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.address like CONCAT('%',#{params.searchWord},'%')
                  </if>
                  <if test="params.searchKey == 5 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.username like CONCAT('%',#{params.searchWord},'%')
                  </if>
                  <if test="params.searchKey == 6 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.comment like CONCAT('%',#{params.searchWord},'%')
                  </if>
                  <if test="params.searchKey == 7 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.comment1 like CONCAT('%',#{params.searchWord},'%')
                  </if>
                  <if test="params.searchKey == 8 and params.searchWord!=null and params.searchWord!=''">
                        AND t1.comment like CONCAT('%',#{params.searchWord},'%')
                  </if>
            </where>
      </sql>
    <sql id="commonWhereV1">
        <where>
            1 = 1
            <if test="params.productCategoryType !=null">
                <choose>
                    <when test="params.productCategoryType == 1">
                        and charindex(','+ '固定资产' +',',','+ t2.product_category +',')>0
                    </when>
                    <when test="params.productCategoryType == 2">
                        and charindex(','+ '常用资产' +',',','+ t2.product_category +',')>0
                    </when>
                    <otherwise>
                        and (charindex(','+ '固定资产' +',',','+ t2.product_category +',')>0 or charindex(','+ '常用资产' +',',','+ t2.product_category +',')>0)
                    </otherwise>
                </choose>

            </if>
            <if test="params.authorizeId != null">
                and t1.authorizeid = #{params.authorizeId}
            </if>
            <if test="params.kind != null">
                and t2.kind = #{params.kind}
            </if>
            <if test="params.isTaxModel != null and params.isTaxModel">
                and t2.kind = 3 and t2.invoicing_flag = 1
            </if>
            <if test="params.passFlag != null">
                and t2.channel_state = #{params.passFlag}
            </if>
            <if test="params.attrBusinessSection != null">
                and t1.attr_business_section = #{params.attrBusinessSection}
            </if>
            <if test="params.cooperationId != null">
                and t2.partner_company = #{params.cooperationId}
            </if>
            <if test="params.attributionArea != null and params.attributionArea.size()>0">
                and
                <foreach collection="params.attributionArea" item="item" open="(" close=")" separator="or">
                    t1.attribution_area like CONCAT('%', #{item},'%')
                </foreach>
            </if>
            <if test="(params.provinceIdList != null and params.provinceIdList.size()>0)
                              ||(params.cityIdList != null and params.cityIdList.size()>0)
                              ||(params.countyIdList != null and params.countyIdList.size()>0)">
                and (
                1 > 1
                <if test="(params.provinceIdList != null and params.provinceIdList.size()>0) ">
                    or t3.province_id in
                    <foreach collection="params.provinceIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="(params.cityIdList != null and params.cityIdList.size()>0) ">
                    or t3.city_id in
                    <foreach collection="params.cityIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="(params.countyIdList != null and params.countyIdList.size()>0) ">
                    or t3.county_id in
                    <foreach collection="params.countyIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="params.idList != null and params.idList.size()>0">
                and t1.id in
                <foreach collection="params.idList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.classification!=null">
                AND t1.classification like CONCAT('%',#{params.classification},'%')
            </if>
            <if test="params.brandIdList != null and params.brandIdList.size()>0">
                and
                <foreach collection="params.brandIdList" item="item" open="(" close=")" separator=" or ">
                    t1.brandid like CONCAT('%,', #{item},',%')
                </foreach>
            </if>
            <if test="params.channelLevel != null">
                AND isnull(t1.QuDaoLevel,0) = #{params.channelLevel}
            </if>
            <if test="params.searchKey == 0 and params.searchWord!=null and params.searchWord!=''">
                AND (t1.company like CONCAT('%',#{params.searchWord},'%') or t1.username like
                CONCAT('%',#{params.searchWord},'%'))
            </if>
            <if test="params.searchKey == 1 and params.searchWord!=null and params.searchWord!=''">
                AND t1.tel like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 2 and params.searchWord!=null and params.searchWord!=''">
                AND (t1.QQ like CONCAT('%',#{params.searchWord},'%') or t1.wangwang like CONCAT('%',#{params.searchWord},'%'))
            </if>
            <if test="params.searchKey == 3 and params.searchWord!=null and params.searchWord!=''">
                AND t1.id = #{params.searchWord}
            </if>
            <if test="params.searchKey == 4 and params.searchWord!=null and params.searchWord!=''">
                AND t1.address like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 5 and params.searchWord!=null and params.searchWord!=''">
                AND t1.username like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 6 and params.searchWord!=null and params.searchWord!=''">
                AND t1.comment like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 7 and params.searchWord!=null and params.searchWord!=''">
                AND t1.comment1 like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 8 and params.searchWord!=null and params.searchWord!=''">
                AND t1.comment like CONCAT('%',#{params.searchWord},'%')
            </if>
            <if test="params.searchKey == 9 and params.searchWord!=null and params.searchWord!=''">
                AND t4.userName = #{params.searchWord}
            </if>
            <if test="params.searchKey == 10 and params.searchWord!=null and params.searchWord!=''">
                AND t1.company_jc like CONCAT('%',#{params.searchWord},'%')
            </if>
        </where>
    </sql>
</mapper>
